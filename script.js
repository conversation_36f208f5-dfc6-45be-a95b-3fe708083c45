// Language switching functionality
document.addEventListener("DOMContentLoaded", function () {
  const langButtons = document.querySelectorAll(".lang-btn");
  const html = document.documentElement;
  const body = document.body;

  // Translation content
  const translations = {
    ar: {
      title: 'معرض "صنع في ليبيا" - Made in Libya Exhibition',
      headerArabic: "جودة وطنية بأسواق واعدة",
      headerEnglish: "National quality in promising markets",
      mainTitle:
        'يُعد معرض "صنع في ليبيا" منصة وطنية حيوية تهدف إلى تسليط الضوء على قدرات وإمكانيات القطاع الصناعي والإنتاجي الليبي',
      description: [
        "تعزيز ثقافة المنتج المحلي لدى المستهلكين، وفتح آفاق جديدة للشركات والمصانع الليبية نحو النمو والتوسع.",
        "تنظيم هذا المعرض، في مدينة بنغازي، العاصمة الاقتصادية والتجارية الثانية لليبيا، ليؤكد على أهمية المدينة كمركز حيوي للنشاط الاقتصادي وناقذة للمنتجات الليبية نحو الأسواق المحلية والإقليمية.",
        "شركة أجيال للخدمات الإعلامية والمعارض والمؤتمرات، بخبرتها الواسعة، مسؤولة تنظم هذا الحدث الهام لضمان تقديمه بأعلى معايير الجودة والاحترافية",
      ],
      navigation: {
        home: "الرئيسية",
        vision: "رؤية المعرض",
        message: "الرسالة",
        objectives: "أهداف المعرض",
        companies: "الشركات الليبية",
        audience: "الجمهور المستهدف",
      },
    },
    en: {
      title: 'Made in Libya Exhibition - معرض "صنع في ليبيا"',
      headerArabic: "National quality in promising markets",
      headerEnglish: "جودة وطنية بأسواق واعدة",
      mainTitle:
        'The "Made in Libya" exhibition is a vital national platform aimed at highlighting the capabilities and potential of the Libyan industrial and productive sector',
      description: [
        "Promoting the culture of local products among consumers, and opening new horizons for Libyan companies and factories towards growth and expansion.",
        "Organizing this exhibition in the city of Benghazi, Libya's second economic and commercial capital, confirms the importance of the city as a vital center for economic activity and a gateway for Libyan products to local and regional markets.",
        "Ajyal Company for Media Services, Exhibitions and Conferences, with its extensive experience, is responsible for organizing this important event to ensure its delivery to the highest standards of quality and professionalism.",
      ],
      navigation: {
        home: "Home",
        vision: "Exhibition Vision",
        message: "Message",
        objectives: "Objectives",
        companies: "Libyan Companies",
        audience: "Target Audience",
      },
    },
  };

  // Function to switch language
  function switchLanguage(lang) {
    const isArabic = lang === "ar";

    // Update HTML attributes
    html.setAttribute("lang", lang);
    html.setAttribute("dir", isArabic ? "rtl" : "ltr");

    // Update document title
    document.title = translations[lang].title;

    // Update button states
    langButtons.forEach((btn) => {
      const btnLang = btn.getAttribute("data-lang");
      btn.classList.toggle("active", btnLang === lang);
    });

    // Update content
    const arabicTitle = document.querySelector(".arabic-title");
    const englishSubtitle = document.querySelector(".english-subtitle");
    const mainTitle = document.querySelector(".main-title");
    const descriptionText = document.querySelector(".description-text");

    if (arabicTitle) arabicTitle.textContent = translations[lang].headerArabic;
    if (englishSubtitle)
      englishSubtitle.textContent = translations[lang].headerEnglish;
    if (mainTitle) mainTitle.textContent = translations[lang].mainTitle;

    if (descriptionText) {
      descriptionText.innerHTML = translations[lang].description
        .map((text) => `<p>${text}</p>`)
        .join("");
    }

    // Update navigation
    const navLinks = document.querySelectorAll(".nav-link");
    navLinks.forEach((link) => {
      const href = link.getAttribute("href");
      const key = href.replace("#", "");
      if (translations[lang].navigation[key]) {
        link.textContent = translations[lang].navigation[key];
      }
    });

    // Update all elements with data attributes
    const elementsWithData = document.querySelectorAll("[data-ar][data-en]");
    elementsWithData.forEach((element) => {
      const text = element.getAttribute(`data-${lang}`);
      if (text) {
        element.textContent = text;
      }
    });

    // Store language preference
    localStorage.setItem("preferred-language", lang);
  }

  // Event listeners for language buttons
  langButtons.forEach((btn) => {
    btn.addEventListener("click", () => {
      const lang = btn.getAttribute("data-lang");
      switchLanguage(lang);
    });
  });

  // Smooth scroll navigation functionality
  function scrollToSection(sectionId) {
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
      targetSection.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }

    // Update navigation active state
    const navLinks = document.querySelectorAll(".nav-link");
    navLinks.forEach((link) => {
      link.classList.remove("active");
      if (link.getAttribute("href") === `#${sectionId}`) {
        link.classList.add("active");
      }
    });
  }

  // Add navigation event listeners
  const navLinks = document.querySelectorAll(".nav-link");
  navLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      e.preventDefault();
      const sectionId = link.getAttribute("href").replace("#", "");
      scrollToSection(sectionId);
    });
  });

  // Intersection Observer for updating active navigation
  const observerOptions = {
    root: null,
    rootMargin: "-50% 0px -50% 0px",
    threshold: 0,
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const sectionId = entry.target.id;
        const navLinks = document.querySelectorAll(".nav-link");
        navLinks.forEach((link) => {
          link.classList.remove("active");
          if (link.getAttribute("href") === `#${sectionId}`) {
            link.classList.add("active");
          }
        });
      }
    });
  }, observerOptions);

  // Observe all sections
  const sections = document.querySelectorAll(".section");
  sections.forEach((section) => {
    observer.observe(section);
  });

  // Initialize with saved language or default to Arabic
  const savedLang = localStorage.getItem("preferred-language") || "ar";
  switchLanguage(savedLang);

  // Smooth scroll animation for better UX
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });

  // Add fade-in animation on load
  body.style.opacity = "0";
  body.style.transition = "opacity 0.5s ease-in-out";

  setTimeout(() => {
    body.style.opacity = "1";
  }, 100);

  // Scroll Animation System
  function initScrollAnimations() {
    const animatedElements = document.querySelectorAll(
      ".animate-on-scroll, .animate-fade-in, .animate-slide-left, .animate-slide-right, .animate-scale, .animate-stagger, .animate-card, .animate-image, .animate-title, .animate-text"
    );

    const scrollObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Add a small delay to make the animation more noticeable
            const delay = entry.target.classList.contains("animate-delay-1")
              ? 100
              : entry.target.classList.contains("animate-delay-2")
              ? 200
              : entry.target.classList.contains("animate-delay-3")
              ? 300
              : entry.target.classList.contains("animate-delay-4")
              ? 400
              : entry.target.classList.contains("animate-delay-5")
              ? 500
              : entry.target.classList.contains("animate-delay-6")
              ? 600
              : 50;

            setTimeout(() => {
              entry.target.classList.add("animate-in");
            }, delay);

            // Stop observing this element once it's animated
            scrollObserver.unobserve(entry.target);
          }
        });
      },
      {
        threshold: 0.15,
        rootMargin: "0px 0px -100px 0px",
      }
    );

    animatedElements.forEach((element) => {
      scrollObserver.observe(element);

      // Check if element is already in viewport and animate immediately
      const rect = element.getBoundingClientRect();
      const isInViewport =
        rect.top < window.innerHeight * 0.8 && rect.bottom > 0;

      if (isInViewport) {
        const delay = element.classList.contains("animate-delay-1")
          ? 100
          : element.classList.contains("animate-delay-2")
          ? 200
          : element.classList.contains("animate-delay-3")
          ? 300
          : element.classList.contains("animate-delay-4")
          ? 400
          : element.classList.contains("animate-delay-5")
          ? 500
          : element.classList.contains("animate-delay-6")
          ? 600
          : 50;

        setTimeout(() => {
          element.classList.add("animate-in");
          scrollObserver.unobserve(element);
        }, delay);
      }
    });
  }

  // Immediately animate elements in the active section (home section)
  setTimeout(() => {
    const activeSection = document.querySelector(".section.active");
    if (activeSection) {
      const activeElements = activeSection.querySelectorAll(
        ".animate-on-scroll, .animate-fade-in, .animate-slide-left, .animate-slide-right, .animate-scale, .animate-stagger, .animate-card, .animate-image, .animate-title, .animate-text"
      );

      activeElements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add("animate-in");
        }, index * 200); // Stagger the animations
      });
    }
  }, 300);

  // Initialize scroll animations
  initScrollAnimations();

  // Initialize AOS (Animate On Scroll) library
  if (typeof AOS !== "undefined") {
    // Check if device is mobile
    const isMobile = window.innerWidth <= 768;

    AOS.init({
      duration: isMobile ? 600 : 1000, // Shorter duration on mobile
      easing: "ease-in-out-cubic", // Easing function
      once: true, // Whether animation should happen only once
      mirror: false, // Whether elements should animate out while scrolling past them
      offset: isMobile ? 50 : 100, // Smaller offset on mobile
      delay: 0, // Values from 0 to 3000, with step 50ms
      anchorPlacement: "top-bottom", // Defines which position of the element regarding to window should trigger the animation
      disable: function () {
        // Disable AOS on very small screens or if user prefers reduced motion
        return (
          window.innerWidth < 480 ||
          window.matchMedia("(prefers-reduced-motion: reduce)").matches
        );
      },
    });

    // Refresh AOS on language change to recalculate positions
    document.addEventListener("languageChanged", () => {
      setTimeout(() => {
        AOS.refresh();
      }, 100);
    });

    // Refresh AOS on window resize to handle orientation changes
    window.addEventListener("resize", () => {
      setTimeout(() => {
        AOS.refresh();
      }, 100);
    });
  }

  // Safety fallback: ensure all text is visible after 5 seconds
  setTimeout(() => {
    const hiddenElements = document.querySelectorAll(
      ".animate-on-scroll:not(.animate-in), .animate-fade-in:not(.animate-in), .animate-slide-left:not(.animate-in), .animate-slide-right:not(.animate-in), .animate-scale:not(.animate-in), .animate-stagger:not(.animate-in), .animate-card:not(.animate-in), .animate-image:not(.animate-in), .animate-title:not(.animate-in), .animate-text:not(.animate-in)"
    );

    hiddenElements.forEach((element, index) => {
      setTimeout(() => {
        element.classList.add("animate-in");
      }, index * 50); // Stagger the fallback animations
    });
  }, 5000);

  // Function to add staggered animations to child elements
  function addStaggeredAnimations(
    parentSelector,
    childSelector,
    animationClass = "animate-stagger"
  ) {
    const parents = document.querySelectorAll(parentSelector);

    parents.forEach((parent) => {
      const children = parent.querySelectorAll(childSelector);
      children.forEach((child, index) => {
        child.classList.add(animationClass);
        if (index < 6) {
          child.classList.add(`animate-delay-${index + 1}`);
        }
      });
    });
  }

  // Apply staggered animations to specific components
  setTimeout(() => {
    // Objectives grid items
    addStaggeredAnimations(
      ".objectives-grid",
      ".objective-item",
      "animate-card"
    );

    // Sectors grid items
    addStaggeredAnimations(".sectors-grid", ".sector-card", "animate-card");

    // Audience grid items
    addStaggeredAnimations(
      ".audience-grid",
      ".audience-item",
      "animate-slide-left"
    );

    // Visitors grid items
    addStaggeredAnimations(
      ".visitors-grid",
      ".visitor-category",
      "animate-slide-right"
    );

    // Navigation items
    addStaggeredAnimations(".nav-list", ".nav-link", "animate-fade-in");

    // Terms sections
    addStaggeredAnimations(
      ".terms-content",
      ".terms-section",
      "animate-on-scroll"
    );

    // Design sections
    addStaggeredAnimations(
      ".design-content",
      ".design-section",
      "animate-slide-left"
    );
  }, 500);

  // Initialize Swiper for clients carousel
  const clientsSwiper = new Swiper(".clients-swiper", {
    // Enable infinite loop
    loop: true,

    // Autoplay settings
    autoplay: {
      delay: 0, // No delay between transitions
      disableOnInteraction: false, // Continue autoplay after user interaction
    },

    // Speed of transition
    speed: 3000, // 3 seconds for smooth movement

    // Slides per view
    slidesPerView: "auto",

    // Space between slides
    spaceBetween: 0,

    // Center slides
    centeredSlides: false,

    // Allow touch move
    allowTouchMove: true,

    // Pause on hover
    on: {
      init: function () {
        // Add hover events to pause/resume
        const swiperEl = this.el;
        swiperEl.addEventListener("mouseenter", () => {
          this.autoplay.stop();
        });
        swiperEl.addEventListener("mouseleave", () => {
          this.autoplay.start();
        });
      },
    },
  });
});
